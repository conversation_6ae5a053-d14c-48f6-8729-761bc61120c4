from typing import Dict, List, Optional, Any
import json
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
import datetime
from pathlib import Path

from scipy.signal import find_peaks
import numpy as np
import pandas as pd
from buy_in import calculate_optimal_allocation, print_purchase_plan
from mootdx.quotes import Quotes
from mootdx import consts


# --------------------------- 通用指标 --------------------------- #

def compute_kdj(df: pd.DataFrame, n: int = 9) -> pd.DataFrame:
    if df.empty:
        return df.assign(K=np.nan, D=np.nan, J=np.nan)

    low_n = df["low"].rolling(window=n, min_periods=1).min()
    high_n = df["high"].rolling(window=n, min_periods=1).max()
    rsv = (df["close"] - low_n) / (high_n - low_n + 1e-9) * 100

    K = np.zeros_like(rsv, dtype=float)
    D = np.zeros_like(rsv, dtype=float)
    for i in range(len(df)):
        if i == 0:
            K[i] = D[i] = 50.0
        else:
            K[i] = 2 / 3 * K[i - 1] + 1 / 3 * rsv.iloc[i]
            D[i] = 2 / 3 * D[i - 1] + 1 / 3 * K[i]
    J = 3 * K - 2 * D
    return df.assign(K=K, D=D, J=J)


def compute_bbi(df: pd.DataFrame) -> pd.Series:
    ma3 = df["close"].rolling(3).mean()
    ma6 = df["close"].rolling(6).mean()
    ma12 = df["close"].rolling(12).mean()
    ma24 = df["close"].rolling(24).mean()
    return (ma3 + ma6 + ma12 + ma24) / 4


def compute_rsv(
    df: pd.DataFrame,
    n: int,
) -> pd.Series:
    """
    按公式：RSV(N) = 100 × (C - LLV(L,N)) ÷ (HHV(C,N) - LLV(L,N))
    - C 用收盘价最高值 (HHV of close)
    - L 用最低价最低值 (LLV of low)
    """
    low_n = df["low"].rolling(window=n, min_periods=1).min()
    high_close_n = df["close"].rolling(window=n, min_periods=1).max()
    rsv = (df["close"] - low_n) / (high_close_n - low_n + 1e-9) * 100.0
    return rsv


def compute_dif(df: pd.DataFrame, fast: int = 12, slow: int = 26) -> pd.Series:
    """计算 MACD 指标中的 DIF (EMA fast - EMA slow)。"""
    ema_fast = df["close"].ewm(span=fast, adjust=False).mean()
    ema_slow = df["close"].ewm(span=slow, adjust=False).mean()
    return ema_fast - ema_slow


def bbi_deriv_uptrend(
    bbi: pd.Series,
    *,
    min_window: int,
    max_window: int | None = None,
    q_threshold: float = 0.0,
) -> bool:
    """
    判断 BBI 是否“整体上升”。

    令最新交易日为 T，在区间 [T-w+1, T]（w 自适应，w ≥ min_window 且 ≤ max_window）
    内，先将 BBI 归一化：BBI_norm(t) = BBI(t) / BBI(T-w+1)。

    再计算一阶差分 Δ(t) = BBI_norm(t) - BBI_norm(t-1)。  
    若 Δ(t) 的前 q_threshold 分位数 ≥ 0，则认为该窗口通过；只要存在
    **最长** 满足条件的窗口即可返回 True。q_threshold=0 时退化为
    “全程单调不降”（旧版行为）。

    Parameters
    ----------
    bbi : pd.Series
        BBI 序列（最新值在最后一位）。
    min_window : int
        检测窗口的最小长度。
    max_window : int | None
        检测窗口的最大长度；None 表示不设上限。
    q_threshold : float, default 0.0
        允许一阶差分为负的比例（0 ≤ q_threshold ≤ 1）。
    """
    if not 0.0 <= q_threshold <= 1.0:
        raise ValueError("q_threshold 必须位于 [0, 1] 区间内")

    bbi = bbi.dropna()
    if len(bbi) < min_window:
        return False

    longest = min(len(bbi), max_window or len(bbi))

    # 自最长窗口向下搜索，找到任一满足条件的区间即通过
    for w in range(longest, min_window - 1, -1):
        seg = bbi.iloc[-w:]                # 区间 [T-w+1, T]
        norm = seg / seg.iloc[0]           # 归一化
        diffs = np.diff(norm.values)       # 一阶差分
        if np.quantile(diffs, q_threshold) >= 0:
            return True
    return False


def _find_peaks(
    df: pd.DataFrame,
    *,
    column: str = "high",
    distance: Optional[int] = None,
    prominence: Optional[float] = None,
    height: Optional[float] = None,
    width: Optional[float] = None,
    rel_height: float = 0.5,
    **kwargs: Any,
) -> pd.DataFrame:
    
    if column not in df.columns:
        raise KeyError(f"'{column}' not found in DataFrame columns: {list(df.columns)}")

    y = df[column].to_numpy()

    indices, props = find_peaks(
        y,
        distance=distance,
        prominence=prominence,
        height=height,
        width=width,
        rel_height=rel_height,
        **kwargs,
    )

    peaks_df = df.iloc[indices].copy()
    peaks_df["is_peak"] = True

    # Flatten SciPy arrays into columns (only those with same length as indices)
    for key, arr in props.items():
        if isinstance(arr, (list, np.ndarray)) and len(arr) == len(indices):
            peaks_df[f"peak_{key}"] = arr

    return peaks_df


# --------------------------- Selector 类 --------------------------- #
class BBIKDJSelector:
    """
    自适应 *BBI(导数)* + *KDJ* 选股器
        • BBI: 允许 bbi_q_threshold 比例的回撤
        • KDJ: J < threshold ；或位于历史 J 的 j_q_threshold 分位及以下
        • MACD: DIF > 0
        • 收盘价波动幅度 ≤ price_range_pct
    """

    def __init__(
        self,
        j_threshold: float = -5,
        bbi_min_window: int = 90,
        max_window: int = 90,
        price_range_pct: float = 100.0,
        bbi_q_threshold: float = 0.05,
        j_q_threshold: float = 0.10,
        email_config_file: str = "email_config.json",
        hs300_csv_path: str = "hs300_stocks.csv",
    ) -> None:
        self.j_threshold = j_threshold
        self.bbi_min_window = bbi_min_window
        self.max_window = max_window
        self.price_range_pct = price_range_pct
        self.bbi_q_threshold = bbi_q_threshold  # ← 原 q_threshold
        self.j_q_threshold = j_q_threshold      # ← 新增
        self.email_config_file = email_config_file
        self.hs300_csv_path = hs300_csv_path
        self._stock_names_cache = {}  # 缓存股票名称
        self._hs300_stocks = self._load_hs300_stocks()  # 加载沪深300成分股

    def _load_hs300_stocks(self) -> set:
        """加载沪深300成分股列表"""
        try:
            import os
            if not os.path.exists(self.hs300_csv_path):
                print(f"警告: 沪深300成分股文件 {self.hs300_csv_path} 不存在，将无法标注沪深300成分股")
                return set()

            hs300_df = pd.read_csv(self.hs300_csv_path)
            if 'code' not in hs300_df.columns:
                print(f"警告: 沪深300成分股文件格式错误，缺少 'code' 列")
                return set()

            # 提取股票代码，去掉交易所前缀（如 sh.600000 -> 600000）
            hs300_codes = set()
            for code in hs300_df['code']:
                if isinstance(code, str):
                    # 去掉 sh. 或 sz. 前缀
                    clean_code = code.split('.')[-1] if '.' in code else code
                    hs300_codes.add(clean_code)

            print(f"成功加载 {len(hs300_codes)} 只沪深300成分股")
            return hs300_codes

        except Exception as e:
            print(f"加载沪深300成分股文件时出错: {e}")
            return set()

    def _is_hs300_stock(self, stock_code: str) -> bool:
        """检查股票是否为沪深300成分股"""
        # 去掉可能的交易所前缀
        clean_code = stock_code.split('.')[-1] if '.' in stock_code else stock_code
        return clean_code in self._hs300_stocks

    def _get_stock_name(self, stock_code: str) -> str:
        """获取股票名称，优先从CSV文件读取，如果没有则更新整个市场股票列表"""
        # 先检查内存缓存
        if stock_code in self._stock_names_cache:
            return self._stock_names_cache[stock_code]

        # 尝试从CSV文件读取
        stock_name = self._load_stock_name_from_csv(stock_code)
        if stock_name:
            self._stock_names_cache[stock_code] = stock_name
            return stock_name

        # 如果CSV中没有，更新整个市场的股票列表
        print(f"股票 {stock_code} 不在缓存中，正在更新股票列表...")
        self._update_all_stocks_cache()

        # 再次尝试从缓存中获取
        if stock_code in self._stock_names_cache:
            return self._stock_names_cache[stock_code]

        # 如果还是没找到，返回股票代码
        print(f"警告: 股票 {stock_code} 未在市场列表中找到")
        self._stock_names_cache[stock_code] = stock_code
        return stock_code

    def _update_all_stocks_cache(self):
        """更新所有股票的缓存（上海和深圳市场）"""
        try:
            client = Quotes.factory(market='std')
            all_stocks = []

            # 获取上海市场股票
            print("正在获取上海市场股票列表...")
            sh_stocks = client.stocks(market=consts.MARKET_SH)
            if sh_stocks is not None and not sh_stocks.empty:
                all_stocks.append(sh_stocks)
                print(f"获取到上海市场股票 {len(sh_stocks)} 只")

            # 获取深圳市场股票
            print("正在获取深圳市场股票列表...")
            sz_stocks = client.stocks(market=consts.MARKET_SZ)
            if sz_stocks is not None and not sz_stocks.empty:
                all_stocks.append(sz_stocks)
                print(f"获取到深圳市场股票 {len(sz_stocks)} 只")

            if all_stocks:
                # 合并所有股票数据
                combined_stocks = pd.concat(all_stocks, ignore_index=True)

                # 更新内存缓存
                for _, row in combined_stocks.iterrows():
                    code = str(row['code']).zfill(6)
                    name = row['name']
                    self._stock_names_cache[code] = name

                # 保存到CSV文件
                self._save_all_stocks_to_csv(combined_stocks)
                print(f"✓ 成功缓存 {len(combined_stocks)} 只股票信息")
            else:
                print("❌ 未能获取到任何股票数据")

        except Exception as e:
            print(f"❌ 更新股票缓存失败: {e}")

    def _load_stock_name_from_csv(self, stock_code: str) -> str:
        """从CSV文件中加载股票名称"""
        csv_file = "stock_names.csv"
        try:
            # 确保股票代码是6位字符串格式
            stock_code = str(stock_code).zfill(6)

            if os.path.exists(csv_file):
                df = pd.read_csv(csv_file, dtype={'code': str})  # 确保code列为字符串类型
                if 'code' in df.columns and 'name' in df.columns:
                    # 同时加载所有股票到内存缓存
                    for _, row in df.iterrows():
                        self._stock_names_cache[row['code']] = row['name']

                    # 返回请求的股票名称
                    return self._stock_names_cache.get(stock_code)
            return None
        except Exception as e:
            print(f"从CSV读取股票名称失败: {e}")
            return None

    def _save_all_stocks_to_csv(self, stocks_df: pd.DataFrame):
        """保存所有股票信息到CSV文件"""
        csv_file = "stock_names.csv"
        try:
            # 确保股票代码是6位字符串格式
            stocks_df = stocks_df.copy()
            stocks_df['code'] = stocks_df['code'].astype(str).str.zfill(6)

            # 只保留需要的列
            result_df = stocks_df[['code', 'name']].copy()

            # 去重并排序
            result_df = result_df.drop_duplicates(subset=['code']).sort_values('code')

            # 保存到CSV文件
            result_df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✓ 已保存 {len(result_df)} 只股票信息到 {csv_file}")

        except Exception as e:
            print(f"保存股票信息到CSV失败: {e}")

    def _send_email(self, subject: str, content: str, is_html: bool = False) -> bool:
        """发送邮件"""
        try:
            config_path = Path(self.email_config_file)
            if not config_path.exists():
                print(f"⚠ 邮件配置文件 {self.email_config_file} 不存在，跳过邮件发送")
                return False

            with open(config_path, "r", encoding="utf-8") as f:
                cfg = json.load(f)

            # 检查配置是否完整
            if not all(key in cfg for key in ["host", "port", "auth"]):
                print("⚠ 邮件配置不完整，跳过邮件发送")
                return False

            if not all(key in cfg["auth"] for key in ["user", "pass"]):
                print("⚠ 邮件认证配置不完整，跳过邮件发送")
                return False

            # 检查是否为示例配置
            if cfg["auth"]["user"] == "<EMAIL>" or cfg["auth"]["pass"] == "your_app_password":
                print("⚠ 请先配置邮件账号信息，跳过邮件发送")
                return False

            host = cfg["host"]
            port = cfg["port"]
            user = cfg["auth"]["user"]
            password = cfg["auth"]["pass"]
            receivers = [user]  # 发给自己

            if is_html:
                msg = MIMEMultipart('alternative')
                msg["From"] = user
                msg["To"] = ",".join(receivers)
                msg["Subject"] = Header(subject, "utf-8")

                # 添加HTML内容
                html_part = MIMEText(content, "html", "utf-8")
                msg.attach(html_part)
            else:
                msg = MIMEText(content, "plain", "utf-8")
                msg["From"] = user
                msg["To"] = ",".join(receivers)
                msg["Subject"] = Header(subject, "utf-8")

            with smtplib.SMTP_SSL(host, port) as server:
                server.login(user, password)
                server.sendmail(user, receivers, msg.as_string())

            print(f"✓ 邮件发送成功: {subject}")
            return True

        except Exception as e:
            print(f"❌ 邮件发送失败: {e}")
            return False

    def _send_selection_email(self, picks: List[str], date: pd.Timestamp, data: Dict[str, pd.DataFrame], max_capital: float = 20000):
        """发送选股结果和购买方案邮件"""
        try:
            # 生成邮件内容
            email_content = self._generate_email_content(picks, date, data, max_capital)

            # 生成邮件主题
            today = datetime.date.today().isoformat()
            subject = f"BBIKDJSelector选股结果 - {today} (选股日期: {date.strftime('%Y-%m-%d')})"

            # 发送邮件（HTML格式）
            self._send_email(subject, email_content, is_html=True)

        except Exception as e:
            print(f"❌ 发送选股邮件失败: {e}")

    def _generate_email_content(self, picks: List[str], date: pd.Timestamp, data: Dict[str, pd.DataFrame], max_capital: float = 20000) -> str:
        """生成HTML格式的邮件内容"""

        # 读取HTML模板
        template_path = "email_template.html"
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                html_template = f.read()
        except FileNotFoundError:
            # 如果模板文件不存在，使用简单的HTML格式
            return self._generate_simple_html_content(picks, date, data, max_capital)

        # 生成股票内容部分
        stock_content = self._generate_stock_content(picks, date, data, max_capital)

        # 替换模板中的占位符
        html_content = html_template.format(
            select_date=date.strftime('%Y-%m-%d'),
            generate_time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            stock_count=len(picks),
            max_capital=f"{max_capital:,.2f}",
            stock_content=stock_content
        )

        return html_content

    def _generate_stock_content(self, picks: List[str], date: pd.Timestamp, data: Dict[str, pd.DataFrame], max_capital: float = 20000) -> str:
        """生成股票相关的HTML内容"""
        content = ""

        if picks:
            # 添加选中股票列表
            content += """
        <div class="stock-list">
            <div class="stock-list-header">
                🎯 选中股票列表
            </div>
"""

            # 获取股票价格和名称信息
            valid_data = []
            for i, code in enumerate(picks, 1):
                stock_name = self._get_stock_name(code)
                print(f"获取股票信息: {code} - {stock_name}")

                if code in data:
                    hist = data[code][data[code]["date"] <= date]
                    if not hist.empty:
                        latest_data = hist.iloc[-1]
                        price = float(latest_data["close"])
                        actual_date = latest_data["date"]

                        valid_data.append((code, price))

                        content += f"""
            <div class="stock-item">
                <div>
                    <span class="stock-code">{code}</span>
                    <span class="stock-name">({stock_name})</span>
                </div>
                <div class="price-info">
                    {price:.2f}元 ({actual_date.strftime('%Y-%m-%d')})
                </div>
            </div>
"""
                    else:
                        content += f"""
            <div class="stock-item">
                <div>
                    <span class="stock-code">{code}</span>
                    <span class="stock-name">({stock_name})</span>
                </div>
                <div style="color: #dc3545;">指定日期无数据</div>
            </div>
"""
                else:
                    content += f"""
            <div class="stock-item">
                <div>
                    <span class="stock-code">{code}</span>
                    <span class="stock-name">({stock_name})</span>
                </div>
                <div style="color: #dc3545;">股票数据不存在</div>
            </div>
"""

            content += """
        </div>
"""

            if valid_data:
                # 计算购买方案
                allocation_result = calculate_optimal_allocation(valid_data, max_capital)

                if allocation_result:
                    content += """
        <h3 style="color: #28a745; border-bottom: 2px solid #28a745; padding-bottom: 10px;">
            💼 购买方案详情
        </h3>

        <table class="purchase-table">
            <thead>
                <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>价格(元)</th>
                    <th>股数</th>
                    <th>投入金额(元)</th>
                    <th>资金占比(%)</th>
                </tr>
            </thead>
            <tbody>
"""

                    # 显示每只股票的信息
                    for code, price, share, investment, ratio in zip(
                        allocation_result['codes'],
                        allocation_result['prices'],
                        allocation_result['shares'],
                        allocation_result['investments'],
                        allocation_result['ratios']
                    ):
                        stock_name = self._get_stock_name(code)
                        content += f"""
                <tr>
                    <td><strong>{code}</strong></td>
                    <td>{stock_name}</td>
                    <td>{price:.2f}</td>
                    <td>{share:,}</td>
                    <td>{investment:,.2f}</td>
                    <td>{ratio:.1f}%</td>
                </tr>
"""

                    content += """
            </tbody>
        </table>

        <div class="summary-box">
            <h4 style="margin-top: 0;">📊 资金分配汇总</h4>
"""

                    content += f"""
            <div class="summary-item">
                <span>💰 总投入金额:</span>
                <span><strong>{allocation_result['total_invested']:,.2f}元</strong></span>
            </div>
            <div class="summary-item">
                <span>💵 剩余资金:</span>
                <span><strong>{allocation_result['remaining']:,.2f}元</strong></span>
            </div>
            <div class="summary-item">
                <span>📈 资金利用率:</span>
                <span><strong>{allocation_result['utilization_rate']:.1f}%</strong></span>
            </div>
            <div class="summary-item">
                <span>⚖️ 资金占比差异:</span>
                <span><strong>{allocation_result['ratio_diff']:.1f}%</strong> (最高{allocation_result['max_ratio']:.1f}% - 最低{allocation_result['min_ratio']:.1f}%)</span>
            </div>
        </div>
"""

                    # 平衡性提示
                    if allocation_result['is_balanced']:
                        content += """
        <div class="success">
            ✅ <strong>资金占比均衡</strong> - 各股票资金占比差异在5%以内，分配合理。
        </div>
"""
                    else:
                        content += """
        <div class="warning">
            ⚠️ <strong>资金占比差异较大</strong> - 各股票资金占比差异超过5%，建议关注风险分散。
        </div>
"""

                    content += f"""
        <div class="info-box">
            <strong>📝 说明:</strong> 价格基于选股日期 {date.strftime('%Y-%m-%d')} 或之前最近交易日的收盘价
        </div>
"""
                else:
                    content += """
        <div class="warning">
            ❌ <strong>购买方案计算失败</strong> - 无法生成有效的购买方案
        </div>
"""
            else:
                content += """
        <div class="warning">
            ❌ <strong>没有获取到任何有效的股票数据</strong> - 请检查股票代码和数据源
        </div>
"""
        else:
            content += """
        <div class="no-stocks">
            📭 本次选股未发现符合条件的股票
        </div>
"""

        return content

    def _generate_simple_html_content(self, picks: List[str], date: pd.Timestamp, data: Dict[str, pd.DataFrame], max_capital: float = 20000) -> str:
        """生成简单的HTML格式邮件内容（当模板文件不存在时使用）"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>BBIKDJSelector 选股结果报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; }}
        .stock-item {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 BBIKDJSelector 选股结果报告</h1>
    </div>
    <div class="content">
        <p><strong>选股日期:</strong> {date.strftime('%Y-%m-%d')}</p>
        <p><strong>生成时间:</strong> {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>选中股票数量:</strong> {len(picks)}只</p>
        <p><strong>总资金:</strong> {max_capital:,.2f}元</p>

        {self._generate_stock_content(picks, date, data, max_capital)}

        <hr>
        <p><em>免责声明：本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。</em></p>
    </div>
</body>
</html>
"""
        return html_content

    # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        hist = hist.copy()
        hist["BBI"] = compute_bbi(hist)

        # 0. 收盘价波动幅度约束（最近 max_window 根 K 线）
        win = hist.tail(self.max_window)
        high, low = win["close"].max(), win["close"].min()
        if low <= 0 or (high / low - 1) > self.price_range_pct:
            return False

        # 1. BBI 上升（允许部分回撤）
        if not bbi_deriv_uptrend(
            hist["BBI"],
            min_window=self.bbi_min_window,
            max_window=self.max_window,
            q_threshold=self.bbi_q_threshold,
        ):
            return False

        # 2. KDJ 过滤 —— 双重条件
        kdj = compute_kdj(hist)
        j_today = float(kdj.iloc[-1]["J"])

        # 最近 max_window 根 K 线的 J 分位
        j_window = kdj["J"].tail(self.max_window).dropna()
        if j_window.empty:
            return False
        j_quantile = float(j_window.quantile(self.j_q_threshold))

        if not (j_today < self.j_threshold or j_today <= j_quantile):
            return False

        # 3. MACD：DIF > 0
        hist["DIF"] = compute_dif(hist)
        return hist["DIF"].iloc[-1] > 0



    def _calculate_purchase_plan(self, picks: List[str], date: pd.Timestamp, data: Dict[str, pd.DataFrame], max_capital: float = 20000):
        """计算选中股票的购买方案"""
        print(f"\n{'='*80}")
        print(f"股票购买方案计算 - 选股日期: {date.strftime('%Y-%m-%d')}")
        print(f"{'='*80}")
        print(f"选中股票: {', '.join(picks)}")
        print(f"总资金: {max_capital:.2f}元")
        print(f"股票数量: {len(picks)}只")
        print(f"{'='*80}")

        # 获取选中股票在指定日期的价格
        valid_data = []
        print("正在获取股票价格...")

        for code in picks:
            if code in data:
                hist = data[code][data[code]["date"] <= date]
                if not hist.empty:
                    # 获取指定日期或之前最近的价格数据
                    latest_data = hist.iloc[-1]
                    price = float(latest_data["close"])
                    actual_date = latest_data["date"]

                    valid_data.append((code, price))
                    print(f"✓ {code}: {price:.2f}元 (日期: {actual_date.strftime('%Y-%m-%d')})")
                else:
                    print(f"✗ {code}: 指定日期无数据")
            else:
                print(f"✗ {code}: 股票数据不存在")

        if not valid_data:
            print("\n❌ 没有获取到任何有效的股票数据")
            return

        # 计算购买方案
        print(f"\n成功获取 {len(valid_data)} 只股票价格，开始计算购买方案...")

        # 解包股票代码和价格
        codes, prices = zip(*valid_data)
        n = len(codes)
        target_allocation = max_capital / n  # 目标分配金额
        shares = []
        investments = []

        # 第一轮：为每只股票分配基础股数（股数必须是100的整数倍）
        for price in prices:
            # 计算可购买的手数（1手=100股）
            hands = int(target_allocation // (price * 100))
            share = hands * 100  # 转换为股数
            shares.append(share)
            investments.append(share * price)

        # 计算剩余资金
        total_invested = sum(investments)
        remaining = max_capital - total_invested

        # 第二轮：智能分配剩余资金，确保占比均衡
        while remaining >= min(prices) * 100:
            # 计算当前各股票的资金占比
            current_ratios = [inv / max_capital * 100 for inv in investments]
            target_ratio = 100 / n  # 目标占比

            # 找到占比最低且还能增加100股的股票
            best_idx = -1
            min_ratio = float('inf')

            for i, (ratio, price) in enumerate(zip(current_ratios, prices)):
                # 检查是否还能增加100股
                if remaining >= price * 100:
                    # 计算增加100股后的占比
                    new_investment = investments[i] + price * 100
                    new_ratio = new_investment / max_capital * 100

                    # 确保增加后不会超出目标占比太多（允许5%的差异）
                    if new_ratio <= target_ratio + 5 and ratio < min_ratio:
                        min_ratio = ratio
                        best_idx = i

            # 如果找到合适的股票，增加100股
            if best_idx != -1:
                shares[best_idx] += 100
                investments[best_idx] += prices[best_idx] * 100
                remaining -= prices[best_idx] * 100
            else:
                # 如果没有找到合适的股票，按价格从低到高尝试分配
                sorted_indices = sorted(range(n), key=lambda i: prices[i])
                allocated_this_round = False

                for i in sorted_indices:
                    if remaining >= prices[i] * 100:
                        new_investment = investments[i] + prices[i] * 100
                        new_ratio = new_investment / max_capital * 100

                        # 放宽限制，允许分配
                        if new_ratio <= target_ratio + 8:  # 稍微放宽限制
                            shares[i] += 100
                            investments[i] += prices[i] * 100
                            remaining -= prices[i] * 100
                            allocated_this_round = True
                            break

                # 如果这轮没有分配任何股票，退出循环
                if not allocated_this_round:
                    break

        # 输出结果
        print(f"\n{'='*80}")
        print("股票购买方案")
        print(f"{'='*80}")
        print("股票代码\t价格(元)\t股数\t投入金额(元)\t资金占比(%)")
        print("-" * 80)

        total = 0
        final_investments = []
        for code, price, share in zip(codes, prices, shares):
            investment = share * price
            final_investments.append(investment)
            total += investment

        # 计算并显示每只股票的信息
        for code, price, share, investment in zip(codes, prices, shares, final_investments):
            ratio = (investment / max_capital) * 100
            print(f"{code}\t\t{price:.2f}\t\t{share}\t{investment:.2f}\t\t{ratio:.1f}%")

        print("-" * 80)
        print(f"总投入金额：{total:.2f}元")
        print(f"剩余资金：{max_capital - total:.2f}元")
        print(f"资金利用率：{(total/max_capital)*100:.1f}%")

        # 显示资金占比分析
        ratios = [(inv / max_capital) * 100 for inv in final_investments]
        max_ratio = max(ratios)
        min_ratio = min(ratios)
        ratio_diff = max_ratio - min_ratio

        print(f"资金占比差异：{ratio_diff:.1f}% (最高{max_ratio:.1f}% - 最低{min_ratio:.1f}%)")
        if ratio_diff <= 5:
            print("✓ 资金占比均衡，差异在5%以内")
        else:
            print("⚠ 资金占比差异超过5%")
        print(f"{'='*80}")
        print(f"注意：价格基于选股日期 {date.strftime('%Y-%m-%d')} 或之前最近交易日的收盘价")
        print(f"{'='*80}\n")

    # ---------- 多股票批量 ---------- #
    def select(
        self, date: pd.Timestamp, data: Dict[str, pd.DataFrame]
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            # 额外预留 20 根 K 线缓冲
            hist = hist.tail(self.max_window + 20)
            if self._passes_filters(hist):
                picks.append(code)


        # 计算选中股票的购买方案
        if picks:
            self._calculate_purchase_plan(picks, date, data)

            # 发送邮件通知
            self._send_selection_email(picks, date, data)

        return picks

    def select_with_hs300_annotation(
        self, date: pd.Timestamp, data: Dict[str, pd.DataFrame]
    ) -> List[Dict[str, any]]:
        """
        选股并标注沪深300成分股信息

        返回:
        List[Dict]: 包含股票代码和沪深300标注的字典列表
        每个字典包含:
        - code: 股票代码
        - is_hs300: 是否为沪深300成分股
        - stock_name: 股票名称（如果可获取）
        """
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            # 额外预留 20 根 K 线缓冲
            hist = hist.tail(self.max_window + 20)
            if self._passes_filters(hist):
                picks.append(code)

        # 构建带沪深300标注的结果
        annotated_picks = []
        for code in picks:
            is_hs300 = self._is_hs300_stock(code)
            stock_name = self._get_stock_name(code)

            annotated_picks.append({
                'code': code,
                'is_hs300': is_hs300,
                'stock_name': stock_name,
                'hs300_label': '沪深300' if is_hs300 else '非沪深300'
            })

        # 计算选中股票的购买方案
        if picks:
            self._calculate_purchase_plan(picks, date, data)

            # 发送邮件通知
            self._send_selection_email(picks, date, data)

        return annotated_picks


class PeakKDJSelector:
    """
    Peaks + KDJ 选股器    
    """

    def __init__(
        self,
        j_threshold: float = -5,
        max_window: int = 90,
        fluc_threshold: float = 0.03,
        gap_threshold: float = 0.02,
        j_q_threshold: float = 0.10,
    ) -> None:
        self.j_threshold = j_threshold
        self.max_window = max_window
        self.fluc_threshold = fluc_threshold  # 当日↔peak_(t-n) 波动率上限
        self.gap_threshold = gap_threshold    # oc_prev 必须高于区间最低收盘价的比例
        self.j_q_threshold = j_q_threshold

    # ---------- 单支股票过滤 ---------- #
        # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        if hist.empty:
            return False

        hist = hist.copy().sort_values("date")
        hist["oc_max"] = hist[["open", "close"]].max(axis=1)

        # 1. 提取 peaks
        peaks_df = _find_peaks(
            hist,
            column="oc_max",
            distance=6,
            prominence=0.5,
        )
        
        # 至少两个峰      
        date_today = hist.iloc[-1]["date"]
        peaks_df = peaks_df[peaks_df["date"] < date_today]
        if len(peaks_df) < 2:               
            return False

        peak_t = peaks_df.iloc[-1]          # 最新一个峰
        peaks_list = peaks_df.reset_index(drop=True)
        oc_t = peak_t.oc_max
        total_peaks = len(peaks_list)

        # 2. 回溯寻找 peak_(t-n)
        target_peak = None        
        for idx in range(total_peaks - 2, -1, -1):
            peak_prev = peaks_list.loc[idx]
            oc_prev = peak_prev.oc_max
            if oc_t <= oc_prev:             # 要求 peak_t > peak_(t-n)
                continue

            # 只有当“总峰数 ≥ 3”时才检查区间内其他峰 oc_max
            if total_peaks >= 3 and idx < total_peaks - 2:
                inter_oc = peaks_list.loc[idx + 1 : total_peaks - 2, "oc_max"]
                if not (inter_oc < oc_prev).all():
                    continue

            # 新增： oc_prev 高于区间最低收盘价 gap_threshold
            date_prev = peak_prev.date
            mask = (hist["date"] > date_prev) & (hist["date"] < peak_t.date)
            min_close = hist.loc[mask, "close"].min()
            if pd.isna(min_close):
                continue                    # 区间无数据
            if oc_prev <= min_close * (1 + self.gap_threshold):
                continue

            target_peak = peak_prev
            
            break

        if target_peak is None:
            return False

        # 3. 当日收盘价波动率
        close_today = hist.iloc[-1]["close"]
        fluc_pct = abs(close_today - target_peak.close) / target_peak.close
        if fluc_pct > self.fluc_threshold:
            return False

        # 4. KDJ 过滤
        kdj = compute_kdj(hist)
        j_today = float(kdj.iloc[-1]["J"])
        j_window = kdj["J"].tail(self.max_window).dropna()
        if j_window.empty:
            return False
        j_quantile = float(j_window.quantile(self.j_q_threshold))
        if not (j_today < self.j_threshold or j_today <= j_quantile):
            return False

        return True

    # ---------- 多股票批量 ---------- #
    def select(
        self,
        date: pd.Timestamp,
        data: Dict[str, pd.DataFrame],
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            hist = hist.tail(self.max_window + 20)  # 额外缓冲
            if self._passes_filters(hist):
                picks.append(code)
        return picks
    

class BBIShortLongSelector:
    """
    BBI 上升 + 短/长期 RSV 条件 + DIF > 0 选股器
    """
    def __init__(
        self,
        n_short: int = 3,
        n_long: int = 21,
        m: int = 3,
        bbi_min_window: int = 90,
        max_window: int = 150,
        bbi_q_threshold: float = 0.05,
    ) -> None:
        if m < 2:
            raise ValueError("m 必须 ≥ 2")
        self.n_short = n_short
        self.n_long = n_long
        self.m = m
        self.bbi_min_window = bbi_min_window
        self.max_window = max_window
        self.bbi_q_threshold = bbi_q_threshold   # 新增参数

    # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        hist = hist.copy()
        hist["BBI"] = compute_bbi(hist)

        # 1. BBI 上升（允许部分回撤）
        if not bbi_deriv_uptrend(
            hist["BBI"],
            min_window=self.bbi_min_window,
            max_window=self.max_window,
            q_threshold=self.bbi_q_threshold,
        ):
            return False

        # 2. 计算短/长期 RSV -----------------
        hist["RSV_short"] = compute_rsv(hist, self.n_short)
        hist["RSV_long"] = compute_rsv(hist, self.n_long)

        if len(hist) < self.m:
            return False                        # 数据不足

        win = hist.iloc[-self.m :]              # 最近 m 天
        long_ok = (win["RSV_long"] >= 80).all() # 长期 RSV 全 ≥ 80

        short_series = win["RSV_short"]
        short_start_end_ok = (
            short_series.iloc[0] >= 80 and short_series.iloc[-1] >= 80
        )
        short_has_below_20 = (short_series < 20).any()

        if not (long_ok and short_start_end_ok and short_has_below_20):
            return False

        # 3. MACD：DIF > 0 -------------------
        hist["DIF"] = compute_dif(hist)
        return hist["DIF"].iloc[-1] > 0

    # ---------- 多股票批量 ---------- #
    def select(
        self,
        date: pd.Timestamp,
        data: Dict[str, pd.DataFrame],
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            # 预留足够长度：RSV 计算窗口 + BBI 检测窗口 + m
            need_len = (
                max(self.n_short, self.n_long)
                + self.bbi_min_window
                + self.m
            )
            hist = hist.tail(max(need_len, self.max_window))
            if self._passes_filters(hist):
                picks.append(code)
        return picks


class BreakoutVolumeKDJSelector:
    """
    放量突破 + KDJ + DIF>0 + 收盘价波动幅度 选股器   
    """

    def __init__(
        self,
        j_threshold: float = 0.0,
        up_threshold: float = 3.0,
        volume_threshold: float = 2.0 / 3,
        offset: int = 15,
        max_window: int = 120,
        price_range_pct: float = 10.0,
        j_q_threshold: float = 0.10,        # ← 新增
    ) -> None:
        self.j_threshold = j_threshold
        self.up_threshold = up_threshold
        self.volume_threshold = volume_threshold
        self.offset = offset
        self.max_window = max_window
        self.price_range_pct = price_range_pct
        self.j_q_threshold = j_q_threshold  # ← 新增

    # ---------- 单支股票过滤 ---------- #
    def _passes_filters(self, hist: pd.DataFrame) -> bool:
        if len(hist) < self.offset + 2:
            return False

        hist = hist.tail(self.max_window).copy()

        # ---- 收盘价波动幅度约束 ----
        high, low = hist["close"].max(), hist["close"].min()
        if low <= 0 or (high / low - 1) > self.price_range_pct:
            return False

        # ---- 技术指标 ----
        hist = compute_kdj(hist)
        hist["pct_chg"] = hist["close"].pct_change() * 100
        hist["DIF"] = compute_dif(hist)

        # 0) 指定日约束：J < j_threshold 或位于历史分位；且 DIF > 0
        j_today = float(hist["J"].iloc[-1])

        j_window = hist["J"].tail(self.max_window).dropna()
        if j_window.empty:
            return False
        j_quantile = float(j_window.quantile(self.j_q_threshold))

        # 若不满足任一 J 条件，则淘汰
        if not (j_today < self.j_threshold or j_today <= j_quantile):
            return False
        if hist["DIF"].iloc[-1] <= 0:
            return False

        # ---- 放量突破条件 ----
        n = len(hist)
        wnd_start = max(0, n - self.offset - 1)
        last_idx = n - 1

        for t_idx in range(wnd_start, last_idx):  # 探索突破日 T
            row = hist.iloc[t_idx]

            # 1) 单日涨幅
            if row["pct_chg"] < self.up_threshold:
                continue

            # 2) 相对放量
            vol_T = row["volume"]
            if vol_T <= 0:
                continue
            vols_except_T = hist["volume"].drop(index=hist.index[t_idx])
            if not (vols_except_T <= self.volume_threshold * vol_T).all():
                continue

            # 3) 创新高
            if row["close"] <= hist["close"].iloc[:t_idx].max():
                continue

            # 4) T 之后 J 值维持高位
            if not (hist["J"].iloc[t_idx:last_idx] > hist["J"].iloc[-1] - 10).all():
                continue

            return True  # 满足所有条件

        return False

    # ---------- 多股票批量 ---------- #
    def select(
        self, date: pd.Timestamp, data: Dict[str, pd.DataFrame]
    ) -> List[str]:
        picks: List[str] = []
        for code, df in data.items():
            hist = df[df["date"] <= date]
            if hist.empty:
                continue
            if self._passes_filters(hist):
                picks.append(code)
        return picks
